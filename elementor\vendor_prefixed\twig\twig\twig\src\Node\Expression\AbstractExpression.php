<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 * (c) <PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace ElementorDeps\Twig\Node\Expression;

use ElementorDeps\Twig\Node\Node;
/**
 * Abstract class for all nodes that represents an expression.
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class AbstractExpression extends Node
{
    public function isGenerator() : bool
    {
        return $this->hasAttribute('is_generator') && $this->getAttribute('is_generator');
    }
}
