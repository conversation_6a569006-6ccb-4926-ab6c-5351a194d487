<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace ElementorDeps\Twig\Sandbox;

/**
 * Exception thrown when a not allowed tag is used in a template.
 *
 * <AUTHOR> <<EMAIL>>
 */
final class SecurityNotAllowedTagError extends SecurityError
{
    private $tagName;
    public function __construct(string $message, string $tagName)
    {
        parent::__construct($message);
        $this->tagName = $tagName;
    }
    public function getTagName() : string
    {
        return $this->tagName;
    }
}
