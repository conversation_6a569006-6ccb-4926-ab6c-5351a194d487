<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace ElementorDeps\Twig\Sandbox;

use <PERSON>ementorDeps\Twig\Markup;
use <PERSON>ementorDeps\Twig\Template;
/**
 * Represents a security policy which need to be enforced when sandbox mode is enabled.
 *
 * <AUTHOR> <<EMAIL>>
 */
final class SecurityPolicy implements SecurityPolicyInterface
{
    private $allowedTags;
    private $allowedFilters;
    private $allowedMethods;
    private $allowedProperties;
    private $allowedFunctions;
    public function __construct(array $allowedTags = [], array $allowedFilters = [], array $allowedMethods = [], array $allowedProperties = [], array $allowedFunctions = [])
    {
        $this->allowedTags = $allowedTags;
        $this->allowedFilters = $allowedFilters;
        $this->setAllowedMethods($allowedMethods);
        $this->allowedProperties = $allowedProperties;
        $this->allowedFunctions = $allowedFunctions;
    }
    public function setAllowedTags(array $tags) : void
    {
        $this->allowedTags = $tags;
    }
    public function setAllowedFilters(array $filters) : void
    {
        $this->allowedFilters = $filters;
    }
    public function setAllowedMethods(array $methods) : void
    {
        $this->allowedMethods = [];
        foreach ($methods as $class => $m) {
            $this->allowedMethods[$class] = \array_map(function ($value) {
                return \strtr($value, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz');
            }, \is_array($m) ? $m : [$m]);
        }
    }
    public function setAllowedProperties(array $properties) : void
    {
        $this->allowedProperties = $properties;
    }
    public function setAllowedFunctions(array $functions) : void
    {
        $this->allowedFunctions = $functions;
    }
    public function checkSecurity($tags, $filters, $functions) : void
    {
        foreach ($tags as $tag) {
            if (!\in_array($tag, $this->allowedTags)) {
                throw new SecurityNotAllowedTagError(\sprintf('Tag "%s" is not allowed.', $tag), $tag);
            }
        }
        foreach ($filters as $filter) {
            if (!\in_array($filter, $this->allowedFilters)) {
                throw new SecurityNotAllowedFilterError(\sprintf('Filter "%s" is not allowed.', $filter), $filter);
            }
        }
        foreach ($functions as $function) {
            if (!\in_array($function, $this->allowedFunctions)) {
                throw new SecurityNotAllowedFunctionError(\sprintf('Function "%s" is not allowed.', $function), $function);
            }
        }
    }
    public function checkMethodAllowed($obj, $method) : void
    {
        if ($obj instanceof Template || $obj instanceof Markup) {
            return;
        }
        $allowed = \false;
        $method = \strtr($method, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz');
        foreach ($this->allowedMethods as $class => $methods) {
            if ($obj instanceof $class && \in_array($method, $methods)) {
                $allowed = \true;
                break;
            }
        }
        if (!$allowed) {
            $class = \get_class($obj);
            throw new SecurityNotAllowedMethodError(\sprintf('Calling "%s" method on a "%s" object is not allowed.', $method, $class), $class, $method);
        }
    }
    public function checkPropertyAllowed($obj, $property) : void
    {
        $allowed = \false;
        foreach ($this->allowedProperties as $class => $properties) {
            if ($obj instanceof $class && \in_array($property, \is_array($properties) ? $properties : [$properties])) {
                $allowed = \true;
                break;
            }
        }
        if (!$allowed) {
            $class = \get_class($obj);
            throw new SecurityNotAllowedPropertyError(\sprintf('Calling "%s" property on a "%s" object is not allowed.', $property, $class), $class, $property);
        }
    }
}
